import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Mail, Clock, Award, Shield } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <header className="bg-white shadow-xl sticky top-0 z-50 border-b border-gray-100">
      {/* Top Bar - Hidden on mobile for cleaner experience */}
      <div className="hidden md:block bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 text-white py-4 relative overflow-hidden">
        {/* Subtle animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="construction-pattern"></div>
        </div>

        <div className="container-custom relative z-10">
          <div className="flex flex-col lg:flex-row justify-between items-center">
            {/* Contact Information */}
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-8">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-3 hover:text-orange-300 transition-colors duration-200 group top-banner-item"
              >
                <div className="bg-orange-500/20 p-2 rounded-lg group-hover:bg-orange-500/30 transition-colors duration-200">
                  <Mail className="h-4 w-4 text-orange-400" />
                </div>
                <span className="font-medium text-sm"><EMAIL></span>
              </a>
              <a
                href="tel:+27833467187"
                className="flex items-center space-x-3 hover:text-orange-300 transition-colors duration-200 group top-banner-item"
              >
                <div className="bg-orange-500/20 p-2 rounded-lg group-hover:bg-orange-500/30 transition-colors duration-200">
                  <Phone className="h-4 w-4 text-orange-400" />
                </div>
                <span className="font-bold text-lg tracking-wide">************</span>
              </a>
            </div>

            {/* Business Info & Certifications */}
            <div className="flex items-center space-x-6 mt-3 lg:mt-0">
              <div className="flex items-center space-x-3 group">
                <div className="bg-slate-700/50 p-2 rounded-lg group-hover:bg-slate-600/50 transition-colors duration-200">
                  <Clock className="h-4 w-4 text-orange-400" />
                </div>
                <span className="text-sm font-medium">Mon - Fri: 7am - 5pm</span>
              </div>

              <div className="hidden md:flex items-center space-x-4">
                <div className="flex items-center space-x-2 bg-orange-500/10 px-3 py-1.5 rounded-full border border-orange-500/20 cert-badge">
                  <Award className="h-3 w-3 text-orange-400" />
                  <span className="text-xs font-semibold">12+ Years</span>
                </div>
                <div className="flex items-center space-x-2 bg-blue-500/10 px-3 py-1.5 rounded-full border border-blue-500/20 cert-badge">
                  <Shield className="h-3 w-3 text-blue-400" />
                  <span className="text-xs font-semibold">Certified</span>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* Main Navigation */}
      <nav className="container-custom py-6">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center group logo-container"
            aria-label="Arebone Building Enterprise - Go to homepage"
          >
            <img
              src="/images/arebone logo.png"
              alt="Arebone Building Enterprise Logo"
              className="w-auto object-contain transition-transform duration-300 group-hover:scale-105"
              style={{ height: '80px', maxHeight: '80px', minWidth: '320px', width: 'auto' }}
              loading="eager"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 relative ${location.pathname === item.href
                  ? 'text-slate-800 bg-blue-50 shadow-sm'
                  : 'text-slate-600 hover:text-slate-800 hover:bg-gray-50'
                  }`}
              >
                {item.name}
                {location.pathname === item.href && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-orange-500 rounded-full"></div>
                )}
              </Link>
            ))}

            {/* CTA Buttons */}
            <div className="flex items-center space-x-3 ml-6 pl-6 border-l border-gray-200">
              <a
                href="tel:+27833467187"
                className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Phone className="h-4 w-4" />
                <span>Call Now</span>
              </a>
              <Link
                to="/contact"
                className="bg-slate-700 hover:bg-slate-800 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                Get Quote
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              aria-label="Toggle mobile menu"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden mt-6 pb-6 border-t border-gray-200 bg-gray-50 rounded-b-lg shadow-lg">
            <div className="pt-6 space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`block px-4 py-3 rounded-lg font-medium transition-all duration-200 ${location.pathname === item.href
                    ? 'text-slate-800 bg-orange-50 border-l-4 border-orange-500'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white'
                    }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile CTA Buttons */}
              <div className="pt-4 space-y-3">
                <a
                  href="tel:+27833467187"
                  className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Phone className="h-4 w-4" />
                  <span>Call Now: ************</span>
                </a>
                <Link
                  to="/contact"
                  className="block bg-slate-700 hover:bg-slate-800 text-white font-semibold py-3 px-6 rounded-lg text-center transition-all duration-200 shadow-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Get Free Quote
                </Link>
              </div>

              {/* Mobile Trust Indicators */}
              <div className="pt-4 border-t border-gray-200 mt-4">
                <div className="flex justify-center space-x-6 text-xs text-slate-600">
                  <div className="flex items-center space-x-1">
                    <Award className="h-3 w-3 text-orange-500" />
                    <span>12+ Years Experience</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Shield className="h-3 w-3 text-blue-500" />
                    <span>Certified Professionals</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
